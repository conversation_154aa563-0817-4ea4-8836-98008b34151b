/* ChatGPT Interface Animations - Enhanced */

/* Advanced Welcome Screen Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes welcomeFadeOut {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-40px) scale(0.95);
    }
}

@keyframes slideToBottom {
    from {
        transform: translateY(0);
    }
    to {
        transform: translateY(calc(100vh - 200px));
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(16, 163, 127, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(16, 163, 127, 0.6), 0 0 30px rgba(16, 163, 127, 0.4);
    }
}

/* Enhanced Typing Indicator Animation */
@keyframes typingDot {
    0%, 80%, 100% {
        opacity: 0.3;
        transform: scale(0.8) translateY(0);
    }
    40% {
        opacity: 1;
        transform: scale(1.2) translateY(-4px);
    }
}

@keyframes typingWave {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-10px);
    }
}

/* Enhanced Button Animations */
@keyframes buttonHover {
    0% {
        transform: scale(1) rotate(0deg);
    }
    50% {
        transform: scale(1.1) rotate(2deg);
    }
    100% {
        transform: scale(1.05) rotate(0deg);
    }
}

@keyframes buttonPress {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.95);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes buttonSuccess {
    0% {
        transform: scale(1);
        background-color: #10a37f;
    }
    50% {
        transform: scale(1.1);
        background-color: #0d8f6f;
    }
    100% {
        transform: scale(1);
        background-color: #10a37f;
    }
}

/* Enhanced Input Focus Animation */
@keyframes inputFocus {
    0% {
        border-color: #424242;
        box-shadow: 0 0 0 0 rgba(16, 163, 127, 0);
    }
    100% {
        border-color: #10a37f;
        box-shadow: 0 0 0 3px rgba(16, 163, 127, 0.1);
    }
}

@keyframes inputGlow {
    0%, 100% {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
    50% {
        box-shadow: 0 4px 30px rgba(16, 163, 127, 0.2), 0 0 0 1px rgba(16, 163, 127, 0.1);
    }
}

/* Enhanced Message Entry Animations */
.message-group {
    animation: fadeInUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform-origin: bottom;
}

.user-message {
    animation: slideInFromRight 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.assistant-message {
    animation: slideInFromLeft 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.message-group:nth-child(even) .assistant-message {
    animation-delay: 0.1s;
}

/* Enhanced Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 12px 0;
    animation: fadeInUp 0.3s ease-out;
}

.typing-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: linear-gradient(135deg, #10a37f, #0d8f6f);
    animation: typingDot 1.6s infinite ease-in-out;
    box-shadow: 0 2px 4px rgba(16, 163, 127, 0.3);
}

.typing-dot:nth-child(1) {
    animation-delay: -0.4s;
}

.typing-dot:nth-child(2) {
    animation-delay: -0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0s;
}

/* Message Hover Effects */
.message-group:hover {
    transform: translateY(-2px);
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.message-group:hover .message-text {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transition: box-shadow 0.3s ease;
}

/* Enhanced Button Animations */
.send-button {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.send-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
    transition: all 0.3s ease;
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

.send-button:hover:not(:disabled) {
    animation: buttonHover 0.4s ease;
    box-shadow: 0 4px 15px rgba(236, 236, 236, 0.3);
}

.send-button:hover:not(:disabled)::before {
    width: 100px;
    height: 100px;
}

.send-button:active {
    animation: buttonPress 0.2s ease;
}

.welcome-send-button {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.welcome-send-button:hover:not(:disabled) {
    animation: pulse 0.6s ease infinite;
    box-shadow: 0 0 20px rgba(16, 163, 127, 0.4);
}

/* Enhanced Input Animations */
.message-input, .welcome-message-input {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.input-wrapper:focus-within, .welcome-input-wrapper:focus-within {
    animation: inputFocus 0.3s ease forwards;
    transform: translateY(-2px);
}

.welcome-input-wrapper:focus-within {
    animation: inputGlow 2s ease infinite;
}

/* Smooth Scrolling with Momentum */
.messages-area {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Enhanced Avatar Animations */
.avatar-icon {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
}

.avatar-icon::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.message:hover .avatar-icon {
    transform: scale(1.15) rotate(5deg);
}

.message:hover .avatar-icon::after {
    opacity: 1;
}

/* Enhanced Text Animation */
.message-text {
    animation: fadeInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
}

/* Enhanced Header Animation */
.header-title {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
}

.header-title:hover {
    transform: scale(1.05);
    text-shadow: 0 0 10px rgba(16, 163, 127, 0.3);
}

/* Responsive Animation Adjustments */
@media (max-width: 768px) {
    .message-group {
        animation-duration: 0.2s;
    }

    .typing-indicator {
        padding: 6px 0;
    }

    .typing-dot {
        width: 6px;
        height: 6px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .typing-dot {
        animation: none;
        opacity: 0.6;
    }
}

/* Focus Animations */
.message-input:focus {
    outline: none;
}

/* Loading State Animations */
.send-button:disabled {
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

/* Message Hover Effects */
.message:hover {
    background-color: rgba(255, 255, 255, 0.02);
    border-radius: 8px;
    transition: background-color 0.2s ease;
}

/* Smooth Transitions for All Interactive Elements */
button, input, textarea {
    transition: all 0.2s ease;
}

/* Welcome Message Animation */
.message-group:first-child {
    animation: fadeInUp 0.6s ease-out;
}

/* Welcome Screen Transition Classes */
.welcome-screen.transitioning {
    animation: welcomeFadeOut 0.8s ease-out forwards;
}

.welcome-input-area.slide-down {
    animation: slideToBottom 0.8s ease-out forwards;
}

.main.fade-in {
    animation: scaleIn 0.5s ease-out 0.3s both;
}

.input-area.fade-in {
    animation: fadeInUp 0.5s ease-out 0.5s both;
}

/* Smooth Page Scroll */
html {
    scroll-behavior: smooth;
}

/* Enhanced Message Animations */
.message-group.new-message {
    animation: fadeInUp 0.4s ease-out;
}

.typing-indicator.fade-in {
    animation: fadeInUp 0.3s ease-out;
}

.typing-indicator.fade-out {
    animation: welcomeFadeOut 0.3s ease-out forwards;
}

/* Advanced Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #2d2d2d 25%, #3a3a3a 50%, #2d2d2d 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

.message-loading {
    opacity: 0.7;
    animation: pulse 1.5s ease-in-out infinite;
}

/* Parallax and Depth Effects */
.welcome-screen {
    background: radial-gradient(ellipse at center, rgba(16, 163, 127, 0.05) 0%, transparent 70%);
    animation: glow 4s ease-in-out infinite;
}

.message-group {
    backdrop-filter: blur(0.5px);
    transition: backdrop-filter 0.3s ease;
}

.message-group:hover {
    backdrop-filter: blur(1px);
}

/* Interactive Feedback */
.ripple-effect {
    position: relative;
    overflow: hidden;
}

.ripple-effect::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::after {
    width: 300px;
    height: 300px;
}

/* Floating Animation for Welcome Elements */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.welcome-title {
    animation: float 3s ease-in-out infinite;
}

.welcome-subtitle {
    animation: float 3s ease-in-out infinite;
    animation-delay: 0.5s;
}

/* Stagger Animation for Multiple Elements */
.message-group:nth-child(1) { animation-delay: 0s; }
.message-group:nth-child(2) { animation-delay: 0.1s; }
.message-group:nth-child(3) { animation-delay: 0.2s; }
.message-group:nth-child(4) { animation-delay: 0.3s; }
.message-group:nth-child(5) { animation-delay: 0.4s; }

/* Enhanced Focus States */
.input-wrapper:focus-within,
.welcome-input-wrapper:focus-within {
    position: relative;
}

.input-wrapper:focus-within::before,
.welcome-input-wrapper:focus-within::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #10a37f, #0d8f6f, #10a37f);
    border-radius: inherit;
    z-index: -1;
    animation: glow 2s ease infinite;
}

/* Smooth Page Transitions */
.page-transition {
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Code Block Animations */
.code-block-container {
    animation: fadeInUp 0.5s ease-out;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.code-block-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.copy-button {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.copy-button:hover {
    animation: buttonHover 0.3s ease;
}

.copy-button.copied {
    animation: buttonSuccess 0.5s ease;
}

