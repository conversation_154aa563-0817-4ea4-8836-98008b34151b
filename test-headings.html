<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heading Test - ChatGPT</title>
    <link rel="stylesheet" href="Css/style.css">
    <link rel="stylesheet" href="Css/animations.css">
    <link rel="stylesheet" href="Css/code-highlighting.css">
</head>
<body>
    <div class="app">
        <header class="header">
            <div class="header-content">
                <h1 class="header-title">Heading Test</h1>
            </div>
        </header>

        <main class="main" style="display: block; padding-top: 60px;">
            <div class="chat-container">
                <div class="messages-area">
                    <div class="message-group">
                        <div class="message assistant-message">
                            <div class="message-avatar">
                                <div class="avatar-icon">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="message-content">
                                <div class="message-text">
                                    Here's how the new heading system works:

                                    <h2 class="message-heading-h2">Main Section (H2)</h2>
                                    
                                    This is a major heading created with ***Main Section (H2)***. It has a gradient text effect and an underline accent.

                                    <h3 class="message-heading-h3">Subsection (H3)</h3>
                                    
                                    This is a sub-heading created with **Subsection (H3)**. It has a left border accent and is slightly smaller.

                                    <h2 class="message-heading-h2">Getting Started</h2>
                                    
                                    To use headings in your ChatGPT responses:
                                    
                                    <h3 class="message-heading-h3">For Major Headings</h3>
                                    
                                    Use triple asterisks: ***Your Heading Text***
                                    
                                    <h3 class="message-heading-h3">For Sub-headings</h3>
                                    
                                    Use double asterisks: **Your Subheading Text**

                                    <h2 class="message-heading-h2">Features</h2>
                                    
                                    <h3 class="message-heading-h3">Visual Design</h3>
                                    
                                    • Gradient text effects for H2 headings<br>
                                    • Colored accent lines<br>
                                    • Smooth animations<br>
                                    • Hover effects
                                    
                                    <h3 class="message-heading-h3">Responsive Design</h3>
                                    
                                    • Adapts to different screen sizes<br>
                                    • Mobile-optimized typography<br>
                                    • Consistent spacing
                                    
                                    <h2 class="message-heading-h2">Technical Implementation</h2>
                                    
                                    The system automatically converts markdown-style headings to proper HTML heading tags with custom CSS classes for styling.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
