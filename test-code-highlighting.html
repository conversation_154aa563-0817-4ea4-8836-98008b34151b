<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Highlighting Test</title>
    <link rel="stylesheet" href="Css/style.css">
    <link rel="stylesheet" href="Css/animations.css">
    <link rel="stylesheet" href="Css/code-highlighting.css">
    <!-- Prism.js for syntax highlighting -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</head>
<body>
    <div class="app">
        <header class="header">
            <div class="header-content">
                <h1 class="header-title">Code Highlighting Test</h1>
            </div>
        </header>

        <main class="main" style="display: block; padding-top: 60px;">
            <div class="chat-container">
                <div class="messages-area">
                    <div class="message-group">
                        <div class="message assistant-message">
                            <div class="message-avatar">
                                <div class="avatar-icon">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="message-content">
                                <div class="message-text">
                                    Here's a JavaScript example with syntax highlighting:
                                    
                                    <div class="code-block-container">
                                        <div class="code-block-header">
                                            <span class="code-language">JAVASCRIPT</span>
                                            <button class="copy-button" data-code-id="test-code-1">
                                                <svg class="copy-icon" viewBox="0 0 24 24" fill="none">
                                                    <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" fill="currentColor"/>
                                                </svg>
                                                Copy
                                            </button>
                                        </div>
                                        <div class="code-block-content">
                                            <pre><code class="language-javascript" id="test-code-1">function greetUser(name) {
    // Check if name is provided
    if (!name) {
        console.log("Hello, stranger!");
        return;
    }
    
    const greeting = `Hello, ${name}!`;
    console.log(greeting);
    
    // Return the greeting
    return greeting;
}

// Call the function
greetUser("World");</code></pre>
                                        </div>
                                    </div>
                                    
                                    You can also use inline code like <span class="inline-code">console.log()</span> or <span class="inline-code">document.getElementById()</span>.
                                    
                                    Here's a Python example with line numbers:
                                    
                                    <div class="code-block-container">
                                        <div class="code-block-header">
                                            <span class="code-language">PYTHON</span>
                                            <button class="copy-button" data-code-id="test-code-2">
                                                <svg class="copy-icon" viewBox="0 0 24 24" fill="none">
                                                    <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" fill="currentColor"/>
                                                </svg>
                                                Copy
                                            </button>
                                        </div>
                                        <div class="code-block-content with-line-numbers">
                                            <div class="line-numbers">1
2
3
4
5
6
7
8
9
10
11
12</div>
                                            <div class="code-content-with-numbers">
                                                <pre><code class="language-python" id="test-code-2">class Calculator:
    def __init__(self):
        self.result = 0
    
    def add(self, x, y):
        """Add two numbers and return the result"""
        self.result = x + y
        return self.result
    
    def multiply(self, x, y):
        """Multiply two numbers and return the result"""
        return x * y

# Create an instance and use it
calc = Calculator()
print(calc.add(5, 3))
print(calc.multiply(4, 6))</code></pre>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Apply syntax highlighting
        document.addEventListener('DOMContentLoaded', function() {
            if (window.Prism) {
                Prism.highlightAll();
            }
            
            // Add copy functionality
            document.querySelectorAll('.copy-button').forEach(button => {
                button.addEventListener('click', async function() {
                    const codeId = this.getAttribute('data-code-id');
                    const codeElement = document.getElementById(codeId);
                    
                    if (codeElement) {
                        try {
                            await navigator.clipboard.writeText(codeElement.textContent);
                            showCopyFeedback(this);
                        } catch (err) {
                            console.error('Copy failed:', err);
                        }
                    }
                });
            });
            
            function showCopyFeedback(button) {
                const originalText = button.innerHTML;
                button.classList.add('copied');
                button.innerHTML = `
                    <svg class="copy-icon" viewBox="0 0 24 24" fill="none">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill="currentColor"/>
                    </svg>
                    Copied!
                `;
                
                setTimeout(() => {
                    button.classList.remove('copied');
                    button.innerHTML = originalText;
                }, 2000);
            }
        });
    </script>
</body>
</html>
