/* Code Block Styling for ChatGPT Clone */

/* Code Block Container */
.code-block-container {
    position: relative;
    margin: 16px 0;
    border-radius: 8px;
    background-color: #1e1e1e;
    border: 1px solid #333;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

/* Code Block Header */
.code-block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background-color: #2d2d2d;
    border-bottom: 1px solid #333;
    font-size: 12px;
    color: #8e8ea0;
}

.code-language {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Copy <PERSON> */
.copy-button {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    background-color: #404040;
    border: 1px solid #555;
    border-radius: 4px;
    color: #ececec;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.copy-button:hover {
    background-color: #4a4a4a;
    border-color: #666;
}

.copy-button:active {
    transform: scale(0.98);
}

.copy-button.copied {
    background-color: #10a37f;
    border-color: #10a37f;
    color: white;
}

.copy-icon {
    width: 12px;
    height: 12px;
    fill: currentColor;
}

/* Code Block Content */
.code-block-content {
    position: relative;
    overflow-x: auto;
    background-color: #1e1e1e;
}

.code-block-content pre {
    margin: 0;
    padding: 16px;
    background: transparent !important;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    color: #ececec;
    overflow-x: auto;
    white-space: pre;
}

.code-block-content code {
    background: transparent !important;
    color: inherit;
    font-family: inherit;
    font-size: inherit;
    padding: 0;
}

/* Line Numbers */
.code-block-content.with-line-numbers {
    display: flex;
}

.line-numbers {
    padding: 16px 8px 16px 16px;
    background-color: #252525;
    color: #6a6a6a;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    text-align: right;
    user-select: none;
    border-right: 1px solid #333;
    min-width: 40px;
}

.code-content-with-numbers {
    flex: 1;
    overflow-x: auto;
}

/* Inline Code */
.inline-code {
    background-color: #2d2d2d;
    color: #e96900;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
    border: 1px solid #404040;
}

/* Custom Syntax Highlighting Colors */
.code-block-content .token.comment,
.code-block-content .token.prolog,
.code-block-content .token.doctype,
.code-block-content .token.cdata {
    color: #6a9955;
    font-style: italic;
}

.code-block-content .token.punctuation {
    color: #d4d4d4;
}

.code-block-content .token.property,
.code-block-content .token.tag,
.code-block-content .token.boolean,
.code-block-content .token.number,
.code-block-content .token.constant,
.code-block-content .token.symbol,
.code-block-content .token.deleted {
    color: #b5cea8;
}

.code-block-content .token.selector,
.code-block-content .token.attr-name,
.code-block-content .token.string,
.code-block-content .token.char,
.code-block-content .token.builtin,
.code-block-content .token.inserted {
    color: #ce9178;
}

.code-block-content .token.operator,
.code-block-content .token.entity,
.code-block-content .token.url,
.code-block-content .language-css .token.string,
.code-block-content .style .token.string {
    color: #d4d4d4;
}

.code-block-content .token.atrule,
.code-block-content .token.attr-value,
.code-block-content .token.keyword {
    color: #569cd6;
}

.code-block-content .token.function,
.code-block-content .token.class-name {
    color: #dcdcaa;
}

.code-block-content .token.regex,
.code-block-content .token.important,
.code-block-content .token.variable {
    color: #d16969;
}

/* Scrollbar Styling for Code Blocks */
.code-block-content::-webkit-scrollbar {
    height: 8px;
}

.code-block-content::-webkit-scrollbar-track {
    background: #2d2d2d;
}

.code-block-content::-webkit-scrollbar-thumb {
    background-color: #555;
    border-radius: 4px;
}

.code-block-content::-webkit-scrollbar-thumb:hover {
    background-color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .code-block-container {
        margin: 12px 0;
        border-radius: 6px;
    }
    
    .code-block-header {
        padding: 6px 12px;
        font-size: 11px;
    }
    
    .copy-button {
        padding: 3px 6px;
        font-size: 10px;
    }
    
    .code-block-content pre {
        padding: 12px;
        font-size: 13px;
    }
    
    .line-numbers {
        padding: 12px 6px 12px 12px;
        font-size: 13px;
        min-width: 35px;
    }
}

@media (max-width: 480px) {
    .code-block-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .copy-button {
        align-self: flex-end;
    }
    
    .code-block-content pre {
        font-size: 12px;
    }
    
    .line-numbers {
        font-size: 12px;
    }
}

/* Animation for Copy Feedback */
@keyframes copySuccess {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.copy-button.copied {
    animation: copySuccess 0.3s ease;
}
