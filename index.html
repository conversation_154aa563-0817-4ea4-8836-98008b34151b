<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGPT</title>
    <link rel="stylesheet" href="Css/style.css">
    <link rel="stylesheet" href="Css/animations.css">
    <link rel="stylesheet" href="Css/code-highlighting.css">
    <!-- Prism.js for syntax highlighting -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</head>
<body>
    <div class="app" id="app">
        <!-- Header -->
        <header class="header" id="header">
            <div class="header-content">
                <h1 class="header-title">ChatGPT</h1>
            </div>
        </header>

        <!-- Welcome Screen (Initial State) -->
        <div class="welcome-screen" id="welcomeScreen">
            <div class="welcome-content">
                <div class="welcome-text">
                    <h1 class="welcome-title">Welcome to ChatGPT</h1>
                    <p class="welcome-subtitle">How can I help you today?</p>
                </div>

                <!-- Centered Input Area -->
                <div class="welcome-input-area" id="welcomeInputArea">
                    <div class="welcome-input-container">
                        <div class="welcome-input-wrapper">
                            <textarea
                                id="welcomeInput"
                                class="welcome-message-input"
                                placeholder="Message ChatGPT..."
                                rows="1"
                            ></textarea>
                            <button id="welcomeSendButton" class="welcome-send-button" disabled>
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M7 11V13H17L12.5 17.5L13.92 18.92L21.84 11L13.92 3.08L12.5 4.5L17 9H7V11Z" fill="currentColor"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Chat Area (Hidden Initially) -->
        <main class="main" id="main" style="display: none;">
            <div class="chat-container">
                <!-- Messages Area -->
                <div id="messagesArea" class="messages-area">
                    <!-- Messages will be added here dynamically -->
                </div>
            </div>
        </main>

        <!-- Fixed Input Area (Bottom Position) -->
        <div class="input-area" id="inputArea" style="display: none;">
            <div class="input-container">
                <div class="input-wrapper">
                    <textarea
                        id="questionInput"
                        class="message-input"
                        placeholder="Message ChatGPT..."
                        rows="1"
                    ></textarea>
                    <button id="sendButton" class="send-button" disabled>
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M7 11V13H17L12.5 17.5L13.92 18.92L21.84 11L13.92 3.08L12.5 4.5L17 9H7V11Z" fill="currentColor"/>
                        </svg>
                    </button>
                </div>
                <div class="input-footer">
                    <p class="input-disclaimer">ChatGPT can make mistakes. Consider checking important information.</p>
                </div>
            </div>
        </div>

        <!-- Hidden Clear Button -->
        <button id="clearButton" class="hidden-clear-btn" style="display: none;">Clear</button>
    </div>

    <script src="Backend/Data_Reader.js"></script>
</body>
</html>