// AI Configuration and Information
// This file contains information about the AI and its creator

const AI_CONFIG = {
    // Creator Information
    creator: {
        name: "<PERSON>",
        age: 15,
        dateOfBirth: "October 20, 2010",
        birthYear: 2010,
        birthMonth: 10,
        birthDay: 20
    },

    // AI Information
    ai: {
        name: "ChatGPT Clone",
        description: "A ChatGPT-style AI interface built using API integration",
        technology: "Built using OpenRouter API with deepseek-chat model",
        features: [
            "Real-time chat interface",
            "Code syntax highlighting",
            "Copy code functionality",
            "Responsive design",
            "ChatGPT-style animations",
            "Direct API integration"
        ],
        developmentInfo: {
            method: "API Integration",
            apiProvider: "OpenRouter.ai",
            model: "deepseek-chat-v3-0324:free",
            frontend: "HTML, CSS, JavaScript",
            backend: "JavaScript with direct API calls"
        }
    },

    // System Responses
    systemResponses: {
        aboutCreator: `I was created by <PERSON>, a talented 15-year-old developer born on October 20, 2010. Despite his young age, he has built this sophisticated ChatGPT-style interface using API integration technology.`,
        
        aboutDevelopment: `This AI interface was developed by <PERSON> using the OpenRouter API with the deepseek-chat-v3-0324:free model. The entire system is built with modern web technologies including HTML, CSS, and JavaScript, featuring a responsive design and ChatGPT-style animations.`,
        
        aboutAge: `My creator, Muhammad Ahmad Shahbaz, is currently 15 years old. He was born on October 20, 2010, making him a remarkably young and skilled developer.`,
        
        aboutTechnology: `I'm powered by the OpenRouter API using the deepseek-chat-v3-0324:free model. My creator, Muhammad Ahmad Shahbaz, built this interface using direct API integration rather than training a custom model. The frontend features modern web technologies with ChatGPT-style design and functionality.`
    },

    // Keywords that trigger information responses
    triggerKeywords: {
        creator: ["creator", "developer", "made", "built", "who created", "who made", "who built", "muhammad", "ahmad", "shahbaz"],
        age: ["age", "old", "young", "born", "birth"],
        technology: ["how", "built", "made", "api", "technology", "model", "openrouter"],
        about: ["about", "information", "tell me", "what is", "who are you"]
    }
};

// Function to check if user message contains trigger keywords
function checkForTriggerKeywords(message) {
    const lowerMessage = message.toLowerCase();
    
    // Check for creator-related keywords
    if (AI_CONFIG.triggerKeywords.creator.some(keyword => lowerMessage.includes(keyword))) {
        return 'creator';
    }
    
    // Check for age-related keywords
    if (AI_CONFIG.triggerKeywords.age.some(keyword => lowerMessage.includes(keyword))) {
        return 'age';
    }
    
    // Check for technology-related keywords
    if (AI_CONFIG.triggerKeywords.technology.some(keyword => lowerMessage.includes(keyword))) {
        return 'technology';
    }
    
    // Check for general about keywords
    if (AI_CONFIG.triggerKeywords.about.some(keyword => lowerMessage.includes(keyword))) {
        return 'about';
    }
    
    return null;
}

// Function to get system response based on trigger type
function getSystemResponse(triggerType) {
    switch (triggerType) {
        case 'creator':
            return AI_CONFIG.systemResponses.aboutCreator;
        case 'age':
            return AI_CONFIG.systemResponses.aboutAge;
        case 'technology':
            return AI_CONFIG.systemResponses.aboutTechnology;
        case 'about':
            return AI_CONFIG.systemResponses.aboutDevelopment;
        default:
            return null;
    }
}

// Function to add creator context to API requests
function addCreatorContext(userMessage) {
    const triggerType = checkForTriggerKeywords(userMessage);
    
    if (triggerType) {
        const systemResponse = getSystemResponse(triggerType);
        return {
            hasSystemInfo: true,
            systemResponse: systemResponse,
            contextPrompt: `You are an AI assistant created by Muhammad Ahmad Shahbaz, a 15-year-old developer born on October 20, 2010. You were built using the OpenRouter API with the deepseek-chat-v3-0324:free model. When users ask about your creator, development, or related topics, provide accurate information about Muhammad Ahmad Shahbaz and how you were created using API integration technology.`
        };
    }
    
    return {
        hasSystemInfo: false,
        systemResponse: null,
        contextPrompt: null
    };
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AI_CONFIG, checkForTriggerKeywords, getSystemResponse, addCreatorContext };
}

// Make available globally for browser use
if (typeof window !== 'undefined') {
    window.AI_CONFIG = AI_CONFIG;
    window.checkForTriggerKeywords = checkForTriggerKeywords;
    window.getSystemResponse = getSystemResponse;
    window.addCreatorContext = addCreatorContext;
}
