// Demo responses to test code highlighting functionality
// You can copy these and paste them as AI responses to test the code highlighting

const demoResponses = {
    javascript: `Here's a JavaScript example with async/await:

\`\`\`javascript
async function fetchUserData(userId) {
    try {
        const response = await fetch(\`/api/users/\${userId}\`);
        
        if (!response.ok) {
            throw new Error(\`HTTP error! status: \${response.status}\`);
        }
        
        const userData = await response.json();
        console.log('User data:', userData);
        
        return userData;
    } catch (error) {
        console.error('Failed to fetch user data:', error);
        throw error;
    }
}

// Usage
fetchUserData(123).then(user => {
    document.getElementById('username').textContent = user.name;
});
\`\`\`

You can also use inline code like \`fetch()\` or \`async/await\` for quick references.`,

    python: `Here's a Python class example with decorators:

\`\`\`python
from datetime import datetime
from functools import wraps

def log_execution_time(func):
    """Decorator to log function execution time"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = datetime.now()
        result = func(*args, **kwargs)
        end_time = datetime.now()
        
        execution_time = (end_time - start_time).total_seconds()
        print(f"{func.__name__} executed in {execution_time:.4f} seconds")
        
        return result
    return wrapper

class DataProcessor:
    def __init__(self, data_source):
        self.data_source = data_source
        self.processed_data = []
    
    @log_execution_time
    def process_data(self):
        """Process data from the source"""
        for item in self.data_source:
            # Simulate processing
            processed_item = {
                'id': item.get('id'),
                'value': item.get('value', 0) * 2,
                'timestamp': datetime.now().isoformat()
            }
            self.processed_data.append(processed_item)
        
        return self.processed_data

# Usage example
data = [{'id': 1, 'value': 10}, {'id': 2, 'value': 20}]
processor = DataProcessor(data)
result = processor.process_data()
\`\`\`

The \`@log_execution_time\` decorator automatically logs how long each method takes to execute.`,

    html: `Here's an HTML5 structure with semantic elements:

\`\`\`html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Web Page</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header class="main-header">
        <nav class="navigation">
            <ul class="nav-list">
                <li><a href="#home">Home</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </nav>
    </header>
    
    <main class="main-content">
        <section class="hero-section">
            <h1>Welcome to Our Website</h1>
            <p>This is a modern, semantic HTML5 structure.</p>
            <button class="cta-button">Get Started</button>
        </section>
        
        <article class="content-article">
            <h2>Article Title</h2>
            <p>Article content goes here...</p>
        </article>
    </main>
    
    <footer class="main-footer">
        <p>&copy; 2024 Your Company. All rights reserved.</p>
    </footer>
    
    <script src="script.js"></script>
</body>
</html>
\`\`\`

This uses semantic HTML5 elements like \`<header>\`, \`<nav>\`, \`<main>\`, \`<section>\`, \`<article>\`, and \`<footer>\`.`,

    css: `Here's modern CSS with Grid and Flexbox:

\`\`\`css
/* Modern CSS Grid and Flexbox Layout */
.container {
    display: grid;
    grid-template-areas: 
        "header header"
        "sidebar main"
        "footer footer";
    grid-template-rows: auto 1fr auto;
    grid-template-columns: 250px 1fr;
    min-height: 100vh;
    gap: 1rem;
}

.header {
    grid-area: header;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar {
    grid-area: sidebar;
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
}

.main-content {
    grid-area: main;
    padding: 1rem;
}

.card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        grid-template-areas: 
            "header"
            "main"
            "sidebar"
            "footer";
        grid-template-columns: 1fr;
    }
}
\`\`\`

This CSS uses modern features like \`grid-template-areas\`, \`flexbox\`, and \`CSS custom properties\`.`,

    sql: `Here's a complex SQL query with joins and window functions:

\`\`\`sql
-- Complex SQL query with CTEs, window functions, and joins
WITH monthly_sales AS (
    SELECT 
        DATE_TRUNC('month', order_date) AS month,
        customer_id,
        SUM(total_amount) AS monthly_total,
        COUNT(*) AS order_count
    FROM orders 
    WHERE order_date >= '2024-01-01'
    GROUP BY DATE_TRUNC('month', order_date), customer_id
),
customer_rankings AS (
    SELECT 
        ms.*,
        c.customer_name,
        c.email,
        ROW_NUMBER() OVER (
            PARTITION BY ms.month 
            ORDER BY ms.monthly_total DESC
        ) AS monthly_rank,
        LAG(ms.monthly_total) OVER (
            PARTITION BY ms.customer_id 
            ORDER BY ms.month
        ) AS previous_month_total
    FROM monthly_sales ms
    JOIN customers c ON ms.customer_id = c.id
)
SELECT 
    month,
    customer_name,
    email,
    monthly_total,
    order_count,
    monthly_rank,
    CASE 
        WHEN previous_month_total IS NULL THEN 'New Customer'
        WHEN monthly_total > previous_month_total THEN 'Increased'
        WHEN monthly_total < previous_month_total THEN 'Decreased'
        ELSE 'Same'
    END AS trend,
    ROUND(
        (monthly_total - COALESCE(previous_month_total, 0)) / 
        NULLIF(previous_month_total, 0) * 100, 2
    ) AS growth_percentage
FROM customer_rankings
WHERE monthly_rank <= 10
ORDER BY month DESC, monthly_rank ASC;
\`\`\`

This query uses CTEs (\`WITH\` clauses), window functions like \`ROW_NUMBER()\` and \`LAG()\`, and complex joins.`
};

// Function to simulate AI responses for testing
function getRandomDemoResponse() {
    const languages = Object.keys(demoResponses);
    const randomLanguage = languages[Math.floor(Math.random() * languages.length)];
    return demoResponses[randomLanguage];
}

console.log('Demo responses loaded. Use getRandomDemoResponse() to get a random code example.');
