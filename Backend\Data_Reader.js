// ChatBot Frontend JavaScript
// Handles user input and direct API communication

class ChatBot {
    constructor() {
        this.isLoading = false;
        this.apiKey = '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';
        this.apiUrl = 'https://openrouter.ai/api/v1/chat/completions';
        this.model = 'deepseek/deepseek-chat-v3:free';
        this.chatHistory = [];
        this.sessionId = this.generateSessionId();
        this.isWelcomeMode = true;
        this.hasStartedChat = false;
        this.initializeEventListeners();
        this.clearHistoryOnLoad();
    }

    generateSessionId() {
        return 'session-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }

    initializeEventListeners() {
        // Get DOM elements for both welcome and chat modes
        this.welcomeInput = document.getElementById('welcomeInput');
        this.welcomeSendButton = document.getElementById('welcomeSendButton');
        this.questionInput = document.getElementById('questionInput');
        this.sendButton = document.getElementById('sendButton');
        this.messagesArea = document.getElementById('messagesArea');
        this.clearButton = document.getElementById('clearButton');
        this.welcomeScreen = document.getElementById('welcomeScreen');
        this.main = document.getElementById('main');
        this.inputArea = document.getElementById('inputArea');

        // Welcome screen event listeners
        if (this.welcomeSendButton) {
            this.welcomeSendButton.addEventListener('click', () => this.handleWelcomeMessage());
        }

        if (this.welcomeInput) {
            // Auto-resize textarea
            this.welcomeInput.addEventListener('input', () => this.autoResizeWelcomeTextarea());

            // Handle Enter key
            this.welcomeInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.handleWelcomeMessage();
                }
            });

            // Enable/disable send button based on input
            this.welcomeInput.addEventListener('input', () => this.updateWelcomeSendButton());
        }

        // Chat mode event listeners
        if (this.sendButton) {
            this.sendButton.addEventListener('click', () => this.sendMessage());
        }

        if (this.questionInput) {
            // Auto-resize textarea
            this.questionInput.addEventListener('input', () => this.autoResizeTextarea());

            // Handle Enter key
            this.questionInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });

            // Enable/disable send button based on input
            this.questionInput.addEventListener('input', () => this.updateSendButton());
        }

        if (this.clearButton) {
            this.clearButton.addEventListener('click', () => this.clearHistory());
        }

        // Initial button states
        this.updateWelcomeSendButton();
        this.updateSendButton();
    }

    autoResizeWelcomeTextarea() {
        const textarea = this.welcomeInput;
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
    }

    autoResizeTextarea() {
        const textarea = this.questionInput;
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
    }

    updateWelcomeSendButton() {
        if (this.welcomeInput && this.welcomeSendButton) {
            const hasText = this.welcomeInput.value.trim().length > 0;
            this.welcomeSendButton.disabled = !hasText || this.isLoading;
        }
    }

    updateSendButton() {
        if (this.questionInput && this.sendButton) {
            const hasText = this.questionInput.value.trim().length > 0;
            this.sendButton.disabled = !hasText || this.isLoading;
        }
    }

    clearHistoryOnLoad() {
        // Clear history when page loads (simulates refresh)
        this.chatHistory = [];
        this.saveHistoryToStorage();
        this.isWelcomeMode = true;
        this.hasStartedChat = false;

        // Ensure welcome screen is visible and chat area is hidden
        if (this.welcomeScreen) {
            this.welcomeScreen.style.display = 'flex';
        }
        if (this.main) {
            this.main.style.display = 'none';
        }
        if (this.inputArea) {
            this.inputArea.style.display = 'none';
        }

        console.log('Chat history cleared on page load');
    }

    async handleWelcomeMessage() {
        if (this.isLoading || !this.welcomeInput) return;

        const question = this.welcomeInput.value.trim();

        if (!question) {
            return;
        }

        // Start the transition to chat mode
        await this.transitionToChatMode(question);
    }

    async transitionToChatMode(firstMessage) {
        this.isWelcomeMode = false;
        this.hasStartedChat = true;

        // Start transition animation
        this.welcomeScreen.classList.add('transitioning');

        // Wait for welcome screen fade out
        await this.sleep(400);

        // Hide welcome screen and show chat interface
        this.welcomeScreen.style.display = 'none';
        this.main.style.display = 'block';
        this.inputArea.style.display = 'block';

        // Add fade-in animations
        this.main.classList.add('fade-in');
        this.inputArea.classList.add('fade-in');

        // Add the first user message
        this.addUserMessage(firstMessage);

        // Clear welcome input
        this.welcomeInput.value = '';

        // Show loading state and get AI response
        this.setLoadingState(true);
        this.showTypingIndicator();

        try {
            // Call OpenRouter API
            const data = await this.callOpenRouterAPI(firstMessage);

            // Remove typing indicator
            this.removeTypingIndicator();

            if (data.success) {
                this.addAssistantMessage(data.response);
            } else {
                this.addAssistantMessage(`I apologize, but I encountered an error: ${data.response}`);
            }

        } catch (error) {
            this.removeTypingIndicator();
            this.addAssistantMessage(`I apologize, but I encountered an error: ${error.message}`);
        } finally {
            this.setLoadingState(false);
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    saveHistoryToStorage() {
        const historyData = {
            conversations: this.chatHistory,
            session_id: this.sessionId,
            created_at: new Date().toISOString()
        };
        localStorage.setItem('chatbot_history', JSON.stringify(historyData));
    }

    loadHistoryFromStorage() {
        try {
            const stored = localStorage.getItem('chatbot_history');
            if (stored) {
                const data = JSON.parse(stored);
                this.chatHistory = data.conversations || [];
                return data;
            }
        } catch (error) {
            console.error('Error loading history:', error);
        }
        return { conversations: [], session_id: this.sessionId, created_at: new Date().toISOString() };
    }

    async callOpenRouterAPI(userMessage) {
        try {
            // Check if message contains creator-related keywords
            const creatorContext = addCreatorContext(userMessage);

            // Prepare messages array with conversation history
            const messages = [];

            // Add system context if creator-related query is detected
            if (creatorContext.hasSystemInfo) {
                messages.push({
                    role: 'system',
                    content: creatorContext.contextPrompt
                });
            }

            // Add recent conversation history (last 5 exchanges to avoid token limits)
            const recentHistory = this.chatHistory.slice(-5);
            for (const conv of recentHistory) {
                messages.push({ role: 'user', content: conv.user_message });
                messages.push({ role: 'assistant', content: conv.bot_response });
            }

            // Add current user message
            messages.push({ role: 'user', content: userMessage });

            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                    'HTTP-Referer': window.location.origin,
                    'X-Title': 'Matrix AI by Muhammad Ahmad Shahbaz'
                },
                body: JSON.stringify({
                    model: this.model,
                    messages: messages,
                    temperature: 0.7,
                    max_tokens: 1500,
                    stream: false
                })
            });

            if (!response.ok) {
                throw new Error(`API request failed: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();

            if (data.choices && data.choices.length > 0) {
                const botResponse = data.choices[0].message.content;

                // Save to history
                const conversationEntry = {
                    timestamp: new Date().toISOString(),
                    user_message: userMessage,
                    bot_response: botResponse,
                    message_id: this.generateMessageId()
                };

                this.chatHistory.push(conversationEntry);
                this.saveHistoryToStorage();

                return {
                    success: true,
                    user_message: userMessage,
                    response: botResponse,
                    session_id: this.sessionId,
                    timestamp: conversationEntry.timestamp,
                    usage: data.usage || {}
                };
            } else {
                throw new Error('No response from AI model');
            }

        } catch (error) {
            console.error('OpenRouter API Error:', error);
            return {
                success: false,
                response: `Error: ${error.message}`
            };
        }
    }

    generateMessageId() {
        return 'msg-' + Date.now() + '-' + Math.random().toString(36).substring(2, 9);
    }

    async sendMessage() {
        // Only allow sending messages in chat mode
        if (this.isLoading || this.isWelcomeMode || !this.hasStartedChat) return;

        const question = this.questionInput.value.trim();

        if (!question) {
            return;
        }

        // Add user message to chat
        this.addUserMessage(question);

        // Clear input and reset height
        this.questionInput.value = '';
        this.autoResizeTextarea();
        this.updateSendButton();

        // Show loading state
        this.setLoadingState(true);
        this.showTypingIndicator();

        try {
            // Call OpenRouter API directly
            const data = await this.callOpenRouterAPI(question);

            // Remove typing indicator
            this.removeTypingIndicator();

            if (data.success) {
                this.addAssistantMessage(data.response);
            } else {
                this.addAssistantMessage(`I apologize, but I encountered an error: ${data.response}`);
            }

        } catch (error) {
            this.removeTypingIndicator();
            this.addAssistantMessage(`I apologize, but I encountered an error: ${error.message}`);
        } finally {
            this.setLoadingState(false);
        }
    }

    addUserMessage(text) {
        const messageGroup = document.createElement('div');
        messageGroup.className = 'message-group new-message';

        messageGroup.innerHTML = `
            <div class="message user-message">
                <div class="message-avatar">
                    <div class="avatar-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" fill="currentColor"/>
                        </svg>
                    </div>
                </div>
                <div class="message-content">
                    <div class="message-text">${this.escapeHtml(text)}</div>
                </div>
            </div>
        `;

        this.messagesArea.appendChild(messageGroup);
        this.scrollToBottom();
    }

    addAssistantMessage(text) {
        const messageGroup = document.createElement('div');
        messageGroup.className = 'message-group new-message';

        // Process text for code blocks and inline code
        const processedText = this.processCodeInText(text);

        messageGroup.innerHTML = `<div class="message assistant-message"><div class="message-avatar"><div class="avatar-icon"><svg width="16" height="16" viewBox="0 0 24 24" fill="none"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/></svg></div></div><div class="message-content"><div class="message-text">${processedText}</div></div></div>`;

        this.messagesArea.appendChild(messageGroup);

        // Apply syntax highlighting to code blocks
        this.applySyntaxHighlighting(messageGroup);

        // Add copy button functionality
        this.addCopyButtonListeners(messageGroup);

        this.scrollToBottom();
    }

    processCodeInText(text) {
        // Store code blocks and inline code temporarily
        const codeBlocks = [];
        const inlineCodes = [];

        // Extract and replace code blocks with placeholders
        let processedText = text.replace(/```(\w+)?\n?([\s\S]*?)```/g, (_, language, code) => {
            const lang = language || 'text';
            const trimmedCode = code.trim();
            const codeId = 'code-' + Date.now() + '-' + Math.random().toString(36).substring(2, 9);

            const codeBlockHtml = `<div class="code-block-container"><div class="code-block-header"><span class="code-language">${lang}</span><button class="copy-button" data-code-id="${codeId}"><svg class="copy-icon" viewBox="0 0 24 24" fill="none"><path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" fill="currentColor"/></svg>Copy</button></div><div class="code-block-content"><pre><code class="language-${lang}" id="${codeId}">${this.escapeHtml(trimmedCode)}</code></pre></div></div>`;

            const placeholder = `__CODE_BLOCK_${codeBlocks.length}__`;
            codeBlocks.push(codeBlockHtml);
            return placeholder;
        });

        // Extract and replace inline code with placeholders
        processedText = processedText.replace(/`([^`]+)`/g, (_, code) => {
            const inlineCodeHtml = `<span class="inline-code">${this.escapeHtml(code)}</span>`;
            const placeholder = `__INLINE_CODE_${inlineCodes.length}__`;
            inlineCodes.push(inlineCodeHtml);
            return placeholder;
        });

        // Escape HTML in remaining text and handle line breaks
        processedText = this.escapeHtml(processedText).replace(/\n/g, '<br>');

        // Restore code blocks
        codeBlocks.forEach((codeBlockHtml, index) => {
            processedText = processedText.replace(`__CODE_BLOCK_${index}__`, codeBlockHtml);
        });

        // Restore inline code
        inlineCodes.forEach((inlineCodeHtml, index) => {
            processedText = processedText.replace(`__INLINE_CODE_${index}__`, inlineCodeHtml);
        });

        return processedText;
    }

    applySyntaxHighlighting(messageElement) {
        // Find all code blocks in the message and apply Prism highlighting
        const codeBlocks = messageElement.querySelectorAll('code[class*="language-"]');
        codeBlocks.forEach(codeBlock => {
            if (window.Prism) {
                Prism.highlightElement(codeBlock);
            }
        });
    }

    addCopyButtonListeners(messageElement) {
        const copyButtons = messageElement.querySelectorAll('.copy-button');
        copyButtons.forEach(button => {
            button.addEventListener('click', async (e) => {
                e.preventDefault();
                const codeId = button.getAttribute('data-code-id');
                const codeElement = document.getElementById(codeId);

                if (codeElement) {
                    try {
                        await navigator.clipboard.writeText(codeElement.textContent);
                        this.showCopyFeedback(button);
                    } catch (err) {
                        // Fallback for older browsers
                        this.fallbackCopyToClipboard(codeElement.textContent);
                        this.showCopyFeedback(button);
                    }
                }
            });
        });
    }

    showCopyFeedback(button) {
        const originalText = button.innerHTML;
        button.classList.add('copied');
        button.innerHTML = `<svg class="copy-icon" viewBox="0 0 24 24" fill="none"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill="currentColor"/></svg>Copied!`;

        setTimeout(() => {
            button.classList.remove('copied');
            button.innerHTML = originalText;
        }, 2000);
    }

    fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
        } catch (err) {
            console.error('Fallback: Oops, unable to copy', err);
        }

        document.body.removeChild(textArea);
    }

    showTypingIndicator() {
        const typingGroup = document.createElement('div');
        typingGroup.className = 'message-group typing-group';
        typingGroup.id = 'typing-indicator';

        typingGroup.innerHTML = `<div class="message assistant-message"><div class="message-avatar"><div class="avatar-icon"><svg width="16" height="16" viewBox="0 0 24 24" fill="none"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/></svg></div></div><div class="message-content"><div class="typing-indicator"><div class="typing-dot"></div><div class="typing-dot"></div><div class="typing-dot"></div></div></div></div>`;

        this.messagesArea.appendChild(typingGroup);
        this.scrollToBottom();
    }

    removeTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    scrollToBottom() {
        // Scroll the entire page to bottom to show the latest message
        setTimeout(() => {
            window.scrollTo({
                top: document.body.scrollHeight,
                behavior: 'smooth'
            });
        }, 100);
    }

    async clearHistory() {
        try {
            this.chatHistory = [];
            this.saveHistoryToStorage();
            this.isWelcomeMode = true;
            this.hasStartedChat = false;

            // Hide chat interface and show welcome screen
            if (this.main) {
                this.main.style.display = 'none';
                this.main.classList.remove('fade-in');
            }
            if (this.inputArea) {
                this.inputArea.style.display = 'none';
                this.inputArea.classList.remove('fade-in');
            }
            if (this.welcomeScreen) {
                this.welcomeScreen.style.display = 'flex';
                this.welcomeScreen.classList.remove('transitioning');
            }

            // Clear messages area
            if (this.messagesArea) {
                this.messagesArea.innerHTML = '';
            }

            // Clear inputs
            if (this.welcomeInput) {
                this.welcomeInput.value = '';
                this.autoResizeWelcomeTextarea();
                this.updateWelcomeSendButton();
            }
            if (this.questionInput) {
                this.questionInput.value = '';
                this.autoResizeTextarea();
                this.updateSendButton();
            }

            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });

            console.log('Chat history cleared manually - returned to welcome screen');

        } catch (error) {
            console.error('Error clearing history:', error);
        }
    }

    setLoadingState(loading) {
        this.isLoading = loading;
        this.updateSendButton();
        this.updateWelcomeSendButton();

        if (this.questionInput) {
            this.questionInput.disabled = loading;
        }
        if (this.welcomeInput) {
            this.welcomeInput.disabled = loading;
        }
    }

    // Utility method to check API status
    checkApiStatus() {
        const status = {
            status: "Matrix AI is ready",
            session_id: this.sessionId,
            model: this.model,
            api_key_configured: !!this.apiKey,
            direct_api: true
        };
        console.log('ChatBot Status:', status);
        return status;
    }
}

// Initialize ChatBot when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.chatBot = new ChatBot();

    // Check API status on load
    const status = window.chatBot.checkApiStatus();
    console.log('✅ Matrix AI initialized with direct API access');
    console.log(`📝 Session ID: ${status.session_id}`);
    console.log(`🎯 Model: ${status.model}`);
    console.log(`🔑 API Key: ${status.api_key_configured ? 'Configured' : 'Not configured'}`);
    console.log('🌐 Using direct OpenRouter API calls');
});