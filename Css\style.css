/* ChatGPT Interface Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #1a1a1a 0%, #212121 50%, #1a1a1a 100%);
    background-attachment: fixed;
    color: #ececec;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.app {
    min-height: 100vh;
    position: relative;
}

/* Enhanced Header */
.header {
    background: rgba(33, 33, 33, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(66, 66, 66, 0.3);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    padding: 0 16px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.header-content {
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 768px;
}

.header-title {
    font-size: 18px;
    font-weight: 600;
    color: #ececec;
    text-align: center;
    width: 100%;
}

/* Welcome Screen */
.welcome-screen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #212121;
    z-index: 50;
    transition: all 0.8s ease;
}

.welcome-screen.fade-out {
    opacity: 0;
    pointer-events: none;
}

.welcome-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 48px;
    max-width: 768px;
    width: 100%;
    padding: 0 24px;
}

.welcome-text {
    text-align: center;
    animation: fadeInUp 1s ease-out;
}

.welcome-title {
    font-size: 48px;
    font-weight: 600;
    color: #ececec;
    margin-bottom: 16px;
    background: linear-gradient(135deg, #ececec 0%, #10a37f 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-subtitle {
    font-size: 20px;
    color: #8e8ea0;
    margin: 0;
    font-weight: 400;
}

/* Welcome Input Area */
.welcome-input-area {
    width: 100%;
    max-width: 600px;
    animation: fadeInUp 1s ease-out 0.3s both;
}

.welcome-input-container {
    width: 100%;
}

.welcome-input-wrapper {
    position: relative;
    background: rgba(47, 47, 47, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 24px;
    border: 1px solid rgba(66, 66, 66, 0.5);
    display: flex;
    align-items: flex-end;
    padding: 16px 20px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.welcome-input-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.welcome-input-wrapper:hover::before {
    left: 100%;
}

.welcome-input-wrapper:focus-within {
    border-color: #10a37f;
    box-shadow: 0 8px 40px rgba(16, 163, 127, 0.3), 0 0 0 1px rgba(16, 163, 127, 0.2);
    transform: translateY(-2px);
}

.welcome-message-input {
    flex: 1;
    background: transparent;
    border: none;
    outline: none;
    color: #ececec;
    font-size: 18px;
    line-height: 1.5;
    resize: none;
    max-height: 200px;
    min-height: 28px;
    font-family: inherit;
    padding: 0;
}

.welcome-message-input::placeholder {
    color: #8e8ea0;
    font-size: 18px;
}

.welcome-send-button {
    background-color: #ececec;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 12px;
    flex-shrink: 0;
}

.welcome-send-button:hover:not(:disabled) {
    background-color: #d1d5db;
    transform: scale(1.05);
}

.welcome-send-button:disabled {
    background-color: #565656;
    cursor: not-allowed;
}

.welcome-send-button svg {
    color: #212121;
}

.welcome-send-button:disabled svg {
    color: #8e8ea0;
}

/* Main Chat Area */
.main {
    padding-top: 60px; /* Account for fixed header */
    padding-bottom: 120px; /* Account for fixed input area */
    min-height: calc(100vh - 60px);
}

.chat-container {
    max-width: 768px;
    margin: 0 auto;
    width: 100%;
}

/* Messages Area */
.messages-area {
    padding: 24px 16px;
    min-height: calc(100vh - 240px); /* Ensure scrollable area */
}

.message-group {
    margin-bottom: 20px;
}

.message {
    display: flex;
    gap: 16px;
    max-width: 100%;
    padding: 8px 12px;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
}

.message:hover {
    background: rgba(255, 255, 255, 0.02);
    transform: translateX(4px);
}

.message-avatar {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #10a37f, #0d8f6f);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 2px 8px rgba(16, 163, 127, 0.3);
    position: relative;
    overflow: hidden;
}

.avatar-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: transform 0.6s ease;
}

.message:hover .avatar-icon::before {
    transform: rotate(45deg) translate(50%, 50%);
}

.user-message .avatar-icon {
    background: linear-gradient(135deg, #ab68ff, #9c5aff);
    box-shadow: 0 2px 8px rgba(171, 104, 255, 0.3);
}

.message-content {
    flex: 1;
    min-width: 0;
}

.message-text {
    font-size: 16px;
    line-height: 1.5;
    color: #ececec;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.user-message {
    margin-bottom: 16px;
}

.user-message .message-text {
    background-color: #2f2f2f;
    padding: 12px 16px;
    border-radius: 18px;
    display: inline-block;
    max-width: fit-content;
}

.assistant-message .message-text {
    padding: 0;
}

/* Message Headings */
.message-heading-h2 {
    font-size: 24px;
    font-weight: 700;
    color: #ececec;
    margin: 24px 0 16px 0;
    line-height: 1.3;
    background: linear-gradient(135deg, #ececec 0%, #10a37f 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    padding-bottom: 8px;
}

.message-heading-h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #10a37f, #0d8f6f);
    border-radius: 2px;
}

.message-heading-h3 {
    font-size: 20px;
    font-weight: 600;
    color: #ececec;
    margin: 20px 0 12px 0;
    line-height: 1.4;
    position: relative;
    padding-left: 16px;
}

.message-heading-h3::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, #10a37f, #0d8f6f);
    border-radius: 2px;
}

/* Enhanced Input Area */
.input-area {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background: rgba(33, 33, 33, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-top: 1px solid rgba(66, 66, 66, 0.3);
    box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.1);
    z-index: 90;
    transition: all 0.3s ease;
}

.input-container {
    max-width: 768px;
    margin: 0 auto;
}

.input-wrapper {
    position: relative;
    background: rgba(47, 47, 47, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 24px;
    border: 1px solid rgba(66, 66, 66, 0.5);
    display: flex;
    align-items: flex-end;
    padding: 12px 16px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.input-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    transition: left 0.5s ease;
}

.input-wrapper:hover::before {
    left: 100%;
}

.input-wrapper:focus-within {
    border-color: #10a37f;
    box-shadow: 0 4px 30px rgba(16, 163, 127, 0.2), 0 0 0 1px rgba(16, 163, 127, 0.1);
    transform: translateY(-2px);
}

.message-input {
    flex: 1;
    background: transparent;
    border: none;
    outline: none;
    color: #ececec;
    font-size: 16px;
    line-height: 1.5;
    resize: none;
    max-height: 200px;
    min-height: 24px;
    font-family: inherit;
    padding: 0;
}

.message-input::placeholder {
    color: #8e8ea0;
}

.send-button {
    background-color: #ececec;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-left: 8px;
    flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
    background-color: #d1d5db;
}

.send-button:disabled {
    background-color: #565656;
    cursor: not-allowed;
}

.send-button svg {
    color: #212121;
}

.send-button:disabled svg {
    color: #8e8ea0;
}

.input-footer {
    text-align: center;
    margin-top: 8px;
}

.input-disclaimer {
    font-size: 12px;
    color: #8e8ea0;
}

/* Hidden Clear Button */
.hidden-clear-btn {
    display: none;
}

/* Loading Animation */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 16px 0;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #8e8ea0;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        opacity: 0.3;
        transform: scale(0.8);
    }
    40% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Scrollbar Styling */
.messages-area::-webkit-scrollbar {
    width: 6px;
}

.messages-area::-webkit-scrollbar-track {
    background: transparent;
}

.messages-area::-webkit-scrollbar-thumb {
    background-color: #424242;
    border-radius: 3px;
}

.messages-area::-webkit-scrollbar-thumb:hover {
    background-color: #565656;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        padding: 0 12px;
    }

    .messages-area {
        padding: 16px 12px;
    }

    .input-area {
        padding: 12px;
    }

    .message {
        gap: 12px;
    }

    .message-text {
        font-size: 15px;
    }

    /* Responsive Headings */
    .message-heading-h2 {
        font-size: 20px;
        margin: 20px 0 14px 0;
    }

    .message-heading-h2::after {
        width: 50px;
        height: 2px;
    }

    .message-heading-h3 {
        font-size: 18px;
        margin: 16px 0 10px 0;
        padding-left: 14px;
    }

    .message-heading-h3::before {
        width: 3px;
        height: 16px;
    }

    /* Welcome Screen Mobile */
    .welcome-content {
        gap: 32px;
        padding: 0 16px;
    }

    .welcome-title {
        font-size: 36px;
    }

    .welcome-subtitle {
        font-size: 18px;
    }

    .welcome-input-wrapper {
        padding: 14px 16px;
    }

    .welcome-message-input {
        font-size: 16px;
    }

    .welcome-send-button {
        width: 32px;
        height: 32px;
        margin-left: 10px;
    }
}

@media (max-width: 480px) {
    .header-title {
        font-size: 16px;
    }

    .message-text {
        font-size: 14px;
    }

    /* Mobile Headings */
    .message-heading-h2 {
        font-size: 18px;
        margin: 16px 0 12px 0;
    }

    .message-heading-h2::after {
        width: 40px;
        height: 2px;
    }

    .message-heading-h3 {
        font-size: 16px;
        margin: 14px 0 8px 0;
        padding-left: 12px;
    }

    .message-heading-h3::before {
        width: 3px;
        height: 14px;
    }

    .input-wrapper {
        padding: 10px 14px;
    }

    /* Welcome Screen Small Mobile */
    .welcome-content {
        gap: 24px;
        padding: 0 12px;
    }

    .welcome-title {
        font-size: 28px;
        margin-bottom: 12px;
    }

    .welcome-subtitle {
        font-size: 16px;
    }

    .welcome-input-wrapper {
        padding: 12px 14px;
    }

    .welcome-message-input {
        font-size: 16px;
    }

    .welcome-message-input::placeholder {
        font-size: 16px;
    }
}