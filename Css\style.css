/* Matrix AI Interface Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Courier New', 'Monaco', '<PERSON>solas', 'Liberation Mono', 'Lucida Console', monospace;
    background: linear-gradient(135deg, #000000 0%, #001100 50%, #000000 100%);
    background-attachment: fixed;
    color: #00ff00;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-shadow: 0 0 2px #00ff0040;
}

.app {
    min-height: 100vh;
    position: relative;
}

/* Matrix Header */
.header {
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 255, 0, 0.3);
    box-shadow: 0 2px 20px rgba(0, 255, 0, 0.1);
    padding: 0 16px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.header-content {
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 768px;
}

.header-title {
    font-size: 18px;
    font-weight: 600;
    color: #00ff00;
    text-align: center;
    width: 100%;
    letter-spacing: 1px;
    text-shadow: 0 0 10px #00ff00;
    font-family: 'Courier New', 'Monaco', 'Consolas', monospace;
}

/* Welcome Screen */
.welcome-screen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #212121;
    z-index: 50;
    transition: all 0.8s ease;
}

.welcome-screen.fade-out {
    opacity: 0;
    pointer-events: none;
}

.welcome-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 48px;
    max-width: 768px;
    width: 100%;
    padding: 0 24px;
}

.welcome-text {
    text-align: center;
    animation: fadeInUp 1s ease-out;
}

.welcome-title {
    font-size: 48px;
    font-weight: 600;
    color: #ececec;
    margin-bottom: 16px;
    background: linear-gradient(135deg, #00ff00 0%, #00cc00 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px #00ff00;
    font-family: 'Courier New', 'Monaco', 'Consolas', monospace;
    letter-spacing: 2px;
}

.welcome-subtitle {
    font-size: 16px;
    color: #00ff00;
    margin: 8px 0;
    font-weight: 500;
    font-family: 'Courier New', 'Monaco', 'Consolas', monospace;
    text-shadow: 0 0 5px #00ff00;
}

.welcome-description {
    font-size: 20px;
    color: #00cc00;
    margin: 0;
    font-weight: 400;
    font-family: 'Courier New', 'Monaco', 'Consolas', monospace;
}

/* Welcome Input Area */
.welcome-input-area {
    width: 100%;
    max-width: 600px;
    animation: fadeInUp 1s ease-out 0.3s both;
}

.welcome-input-container {
    width: 100%;
}

.welcome-input-wrapper {
    position: relative;
    background: rgba(0, 17, 0, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 24px;
    border: 1px solid rgba(0, 255, 0, 0.5);
    display: flex;
    align-items: flex-end;
    padding: 16px 20px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 8px 32px rgba(0, 255, 0, 0.2);
    overflow: hidden;
}

.welcome-input-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.welcome-input-wrapper:hover::before {
    left: 100%;
}

.welcome-input-wrapper:focus-within {
    border-color: #00ff00;
    box-shadow: 0 8px 40px rgba(0, 255, 0, 0.3), 0 0 0 1px rgba(0, 255, 0, 0.2);
    transform: translateY(-2px);
}

.welcome-message-input {
    flex: 1;
    background: transparent;
    border: none;
    outline: none;
    color: #00ff00;
    font-size: 18px;
    line-height: 1.5;
    resize: none;
    max-height: 200px;
    min-height: 28px;
    font-family: 'Courier New', 'Monaco', 'Consolas', monospace;
    padding: 0;
    text-shadow: 0 0 2px #00ff00;
}

.welcome-message-input::placeholder {
    color: #006600;
    font-size: 18px;
    font-family: 'Courier New', 'Monaco', 'Consolas', monospace;
}

.welcome-send-button {
    background-color: #00ff00;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 12px;
    flex-shrink: 0;
    color: #000000;
    box-shadow: 0 0 10px #00ff00;
}

.welcome-send-button:hover:not(:disabled) {
    background-color: #00cc00;
    transform: scale(1.05);
    box-shadow: 0 0 15px #00ff00;
}

.welcome-send-button:disabled {
    background-color: #003300;
    cursor: not-allowed;
    opacity: 0.5;
    box-shadow: none;
}

.welcome-send-button svg {
    color: #212121;
}

.welcome-send-button:disabled svg {
    color: #8e8ea0;
}

/* Main Chat Area */
.main {
    padding-top: 60px; /* Account for fixed header */
    padding-bottom: 120px; /* Account for fixed input area */
    min-height: calc(100vh - 60px);
}

.chat-container {
    max-width: 768px;
    margin: 0 auto;
    width: 100%;
}

/* Messages Area */
.messages-area {
    padding: 24px 16px;
    min-height: calc(100vh - 240px); /* Ensure scrollable area */
}

.message-group {
    margin-bottom: 20px;
}

.message {
    display: flex;
    gap: 16px;
    max-width: 100%;
    padding: 8px 12px;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
}

.message:hover {
    background: rgba(255, 255, 255, 0.02);
    transform: translateX(4px);
}

.message-avatar {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #00ff00, #00cc00);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000000;
    box-shadow: 0 2px 8px rgba(0, 255, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.avatar-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: transform 0.6s ease;
}

.message:hover .avatar-icon::before {
    transform: rotate(45deg) translate(50%, 50%);
}

.user-message .avatar-icon {
    background: linear-gradient(135deg, #00ff00, #00aa00);
    box-shadow: 0 2px 8px rgba(0, 255, 0, 0.4);
}

.message-content {
    flex: 1;
    min-width: 0;
}

.message-text {
    font-size: 16px;
    line-height: 1.5;
    color: #00ff00;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: 'Courier New', 'Monaco', 'Consolas', monospace;
    text-shadow: 0 0 1px #00ff00;
}

.user-message {
    margin-bottom: 16px;
}

.user-message .message-text {
    background-color: #001100;
    padding: 12px 16px;
    border-radius: 18px;
    display: inline-block;
    max-width: fit-content;
    border: 1px solid #00ff00;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.2);
}

.assistant-message .message-text {
    padding: 0;
}



/* Matrix Input Area */
.input-area {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-top: 1px solid rgba(0, 255, 0, 0.3);
    box-shadow: 0 -2px 20px rgba(0, 255, 0, 0.1);
    z-index: 90;
    transition: all 0.3s ease;
}

.input-container {
    max-width: 768px;
    margin: 0 auto;
}

.input-wrapper {
    position: relative;
    background: rgba(0, 17, 0, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 24px;
    border: 1px solid rgba(0, 255, 0, 0.5);
    display: flex;
    align-items: flex-end;
    padding: 12px 16px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 4px 20px rgba(0, 255, 0, 0.2);
    overflow: hidden;
}

.input-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    transition: left 0.5s ease;
}

.input-wrapper:hover::before {
    left: 100%;
}

.input-wrapper:focus-within {
    border-color: #00ff00;
    box-shadow: 0 4px 30px rgba(0, 255, 0, 0.2), 0 0 0 1px rgba(0, 255, 0, 0.1);
    transform: translateY(-2px);
}

.message-input {
    flex: 1;
    background: transparent;
    border: none;
    outline: none;
    color: #00ff00;
    font-size: 16px;
    line-height: 1.5;
    resize: none;
    max-height: 200px;
    min-height: 24px;
    font-family: 'Courier New', 'Monaco', 'Consolas', monospace;
    padding: 0;
    text-shadow: 0 0 2px #00ff00;
}

.message-input::placeholder {
    color: #006600;
    font-family: 'Courier New', 'Monaco', 'Consolas', monospace;
}

.send-button {
    background-color: #00ff00;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-left: 8px;
    flex-shrink: 0;
    box-shadow: 0 0 10px #00ff00;
}

.send-button:hover:not(:disabled) {
    background-color: #00cc00;
    box-shadow: 0 0 15px #00ff00;
}

.send-button:disabled {
    background-color: #003300;
    cursor: not-allowed;
    box-shadow: none;
}

.send-button svg {
    color: #000000;
}

.send-button:disabled svg {
    color: #006600;
}

.input-footer {
    text-align: center;
    margin-top: 8px;
}

.input-disclaimer {
    font-size: 12px;
    color: #006600;
    font-family: 'Courier New', 'Monaco', 'Consolas', monospace;
}

/* Hidden Clear Button */
.hidden-clear-btn {
    display: none;
}

/* Loading Animation */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 16px 0;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #00ff00;
    animation: typing 1.4s infinite ease-in-out;
    box-shadow: 0 0 4px #00ff00;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        opacity: 0.3;
        transform: scale(0.8);
    }
    40% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Matrix Scrollbar Styling */
.messages-area::-webkit-scrollbar {
    width: 6px;
}

.messages-area::-webkit-scrollbar-track {
    background: transparent;
}

.messages-area::-webkit-scrollbar-thumb {
    background-color: #00ff00;
    border-radius: 3px;
    box-shadow: 0 0 4px #00ff00;
}

.messages-area::-webkit-scrollbar-thumb:hover {
    background-color: #00cc00;
    box-shadow: 0 0 6px #00ff00;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        padding: 0 12px;
    }

    .messages-area {
        padding: 16px 12px;
    }

    .input-area {
        padding: 12px;
    }

    .message {
        gap: 12px;
    }

    .message-text {
        font-size: 15px;
    }



    /* Welcome Screen Mobile */
    .welcome-content {
        gap: 32px;
        padding: 0 16px;
    }

    .welcome-title {
        font-size: 36px;
    }

    .welcome-subtitle {
        font-size: 18px;
    }

    .welcome-input-wrapper {
        padding: 14px 16px;
    }

    .welcome-message-input {
        font-size: 16px;
    }

    .welcome-send-button {
        width: 32px;
        height: 32px;
        margin-left: 10px;
    }
}

@media (max-width: 480px) {
    .header-title {
        font-size: 16px;
    }

    .message-text {
        font-size: 14px;
    }



    .input-wrapper {
        padding: 10px 14px;
    }

    /* Welcome Screen Small Mobile */
    .welcome-content {
        gap: 24px;
        padding: 0 12px;
    }

    .welcome-title {
        font-size: 28px;
        margin-bottom: 12px;
    }

    .welcome-subtitle {
        font-size: 12px;
    }

    .welcome-description {
        font-size: 14px;
    }

    .welcome-input-wrapper {
        padding: 12px 14px;
    }

    .welcome-message-input {
        font-size: 16px;
    }

    .welcome-message-input::placeholder {
        font-size: 16px;
    }
}